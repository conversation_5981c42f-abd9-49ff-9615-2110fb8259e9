// uni.request 请求封装
import { uniRequest } from "@/utils/request";
import type { TableData } from "@/utils/type";

let VITE_BASE_URL = "";
// #ifdef APP-PLUS
VITE_BASE_URL = import.meta.env.VITE_BASE_URL_APP;
// #endif
// #ifdef H5
VITE_BASE_URL = import.meta.env.VITE_BASE_URL_H5;
// #endif

/**
 * 财务管理相关接口
 */
enum FinanceType {
  // 记账相关
  ADD_RECORD = "/storeapi/finance/addRecord",
  EDIT_RECORD = "/storeapi/finance/editRecord", 
  DELETE_RECORD = "/storeapi/finance/deleteRecord",
  GET_RECORD_LIST = "/storeapi/finance/getRecordList",
  GET_RECORD_DETAIL = "/storeapi/finance/getRecordDetail",
  
  // 财务统计
  GET_FINANCE_OVERVIEW = "/storeapi/finance/getOverview",
  GET_FINANCE_STATISTICS = "/storeapi/finance/getStatistics",
  
  // 挂账管理
  GET_ACCOUNT_LIST = "/storeapi/finance/getAccountList",
  ADD_ACCOUNT = "/storeapi/finance/addAccount",
  SETTLE_ACCOUNT = "/storeapi/finance/settleAccount",
  
  // 员工工资
  GET_SALARY_LIST = "/storeapi/finance/getSalaryList",
  ADD_SALARY_RECORD = "/storeapi/finance/addSalaryRecord",
  
  // 员工列表（用于发工资时选择员工）
  GET_EMPLOYEE_LIST = "/storeapi/storeuser/getList",
}

/**
 * 记账相关接口
 */

// 新增记账记录
export function addFinanceRecord(data: TableData) {
  return uniRequest({
    url: VITE_BASE_URL + FinanceType.ADD_RECORD,
    method: "POST",
    data,
  });
}

// 编辑记账记录
export function editFinanceRecord(data: TableData) {
  return uniRequest({
    url: VITE_BASE_URL + FinanceType.EDIT_RECORD,
    method: "POST",
    data,
  });
}

// 删除记账记录
export function deleteFinanceRecord(data: TableData) {
  return uniRequest({
    url: VITE_BASE_URL + FinanceType.DELETE_RECORD,
    method: "POST",
    data,
  });
}

// 获取记账记录列表
export function getFinanceRecordList(params: TableData) {
  return uniRequest({
    url: VITE_BASE_URL + FinanceType.GET_RECORD_LIST,
    method: "GET",
    data: params,
  });
}

// 获取记账记录详情
export function getFinanceRecordDetail(params: TableData) {
  return uniRequest({
    url: VITE_BASE_URL + FinanceType.GET_RECORD_DETAIL,
    method: "GET",
    data: params,
  });
}

/**
 * 财务统计相关接口
 */

// 获取财务概览
export function getFinanceOverview(params: TableData) {
  return uniRequest({
    url: VITE_BASE_URL + FinanceType.GET_FINANCE_OVERVIEW,
    method: "GET",
    data: params,
  });
}

// 获取财务统计数据
export function getFinanceStatistics(params: TableData) {
  return uniRequest({
    url: VITE_BASE_URL + FinanceType.GET_FINANCE_STATISTICS,
    method: "GET",
    data: params,
  });
}

/**
 * 挂账管理相关接口
 */

// 获取挂账列表
export function getAccountList(params: TableData) {
  return uniRequest({
    url: VITE_BASE_URL + FinanceType.GET_ACCOUNT_LIST,
    method: "GET",
    data: params,
  });
}

// 新增挂账
export function addAccount(data: TableData) {
  return uniRequest({
    url: VITE_BASE_URL + FinanceType.ADD_ACCOUNT,
    method: "POST",
    data,
  });
}

// 结算挂账
export function settleAccount(data: TableData) {
  return uniRequest({
    url: VITE_BASE_URL + FinanceType.SETTLE_ACCOUNT,
    method: "POST",
    data,
  });
}

/**
 * 工资管理相关接口
 */

// 获取工资记录列表
export function getSalaryList(params: TableData) {
  return uniRequest({
    url: VITE_BASE_URL + FinanceType.GET_SALARY_LIST,
    method: "GET",
    data: params,
  });
}

// 新增工资记录
export function addSalaryRecord(data: TableData) {
  return uniRequest({
    url: VITE_BASE_URL + FinanceType.ADD_SALARY_RECORD,
    method: "POST",
    data,
  });
}

// 获取员工列表（用于发工资时选择员工）
export function getEmployeeList(params: TableData) {
  return uniRequest({
    url: VITE_BASE_URL + FinanceType.GET_EMPLOYEE_LIST,
    method: "GET",
    data: params,
  });
}

/**
 * 记账记录数据类型定义
 */
export interface FinanceRecord {
  id?: number;
  type: 'income' | 'expense' | 'salary'; // 收入、支出、工资
  amount: number; // 金额
  employee_id?: number; // 员工ID（发工资时使用）
  employee_name?: string; // 员工姓名
  date: string; // 日期
  time?: string; // 时间
  start_date?: string; // 开始日期（工资时间段）
  end_date?: string; // 结束日期（工资时间段）
  payment_method: string; // 支付方式：微信、支付宝、现金、其他
  remark?: string; // 备注
  created_at?: string; // 创建时间
  updated_at?: string; // 更新时间
}

/**
 * 员工信息类型定义
 */
export interface Employee {
  id: number;
  name: string;
  phone?: string;
  department?: string;
  position?: string;
  status: number; // 状态：1-在职，0-离职
}
