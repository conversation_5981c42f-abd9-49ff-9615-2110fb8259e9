<template>
  <view class="login">
    <view class="login-content">
      <view class="app-logo">
        <view class="logo-icon"></view>
        <image :src="appConfig.logo" mode="aspectFit"></image>
      </view>
      <view class="app-name">
        {{ appConfig.appName }}
      </view>
      <view class="app-desc">
        {{ appConfig.appDesc }}
      </view>
      <view class="login-form">
        <lInput
          style="margin-bottom: 50rpx"
          v-model="form.phone"
          type="phone"
          shape="white"
        ></lInput>
        <lInput
          style="margin-bottom: 50rpx"
          v-model="form.password"
          type="pass"
          shape="white"
        ></lInput>
        <lButton @click="login">登录</lButton>
      </view>
      <view class="link-view">
        <view class="link-item">
          <text @click="register('register_phone')">新用户注册</text>
        </view>
        <view class="link-item">
          <text @click="register('change_pwd')">忘记密码？</text>
        </view>
      </view>
      <view class="other-login">
        <view class="other-login-title"> 其他快捷登录方式 </view>
        <view class="other-login-view">
          <view
            class="other-login-item"
            v-for="item in otherLoginList"
            :key="item.name"
          >
            <image :src="item.icon" mode="aspectFit"></image>
          </view>
        </view>
      </view>
      <view class="footer-icon">
        <image
          src="@/static/images/login/footer-icon.png"
          mode="aspectFit"
        ></image>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import lInput from "@/components/lInput/lInput.vue";
import lButton from "@/components/lButton/lButton.vue";
import { reactive, ref, onMounted } from "vue";
import { importFile } from "@/utils/file";
import { getUserInfo, loginByAccount } from "@/api/login";
import type { LoginForm } from "./type";
import type { Response } from "@/utils/type";

import CryptoJS from "crypto-js";
const appConfig = ref({
  logo: importFile("../static/images/login/logo.png"),
  appName: "车店助手",
  appDesc: "助力汽修人开源节流创收转型",
});
const otherLoginList = ref([
  {
    icon: importFile("../static/images/login/wechat.png"),
    name: "wechat",
  },
  {
    icon: importFile("../static/images/login/qq.png"),
    name: "QQ",
  },
  {
    icon: importFile("../static/images/login/mac.png"),
    name: "Mac",
  },
]);
const form = reactive<LoginForm>({
  phone: "",
  password: "",
});
const login = async () => {
  if (!form.phone || !form.password) {
    uni.showToast({
      title: "请输入手机号和密码",
      icon: "none",
    });
    return;
  }
  // 验证手机号正则
  const phoneReg = /^1[3-9]\d{9}$/;
  if (!phoneReg.test(form.phone)) {
    uni.showToast({
      title: "请输入正确的手机号",
      icon: "none",
    });
    return;
  }
  const params = {
    phone: form.phone,
    password: form.password,
  };
  // 对密码进行md5加密
  // form.password = CryptoJS.MD5(form.password).toString();
  // 登录请求
  let { code, data }: Response = (await loginByAccount(params)) as Response;
  if (code === 200) {
    uni.setStorageSync("token", data.token);
    uni.setStorageSync("store", data);
    uni.switchTab({
      url: "/pages/home/<USER>",
    });
  } else {
    uni.showToast({
      title: "登录失败",
      icon: "none",
    });
  }
};
// 新用户注册
const register = (code: string) => {
  
  uni.navigateTo({
    url: "/pages/login/register?code=" + code,
  });
};
// 获取app信息
const getAppInfo = async () => {
  let { code, data }: Response = (await getUserInfo({})) as Response;
  if (code === 200) {
    appConfig.value = data;
    appConfig.value.logo = importFile("../static/images/login/logo.png");
  }
};
onMounted(() => {
  // getAppInfo();
});
</script>

<style lang="less" scoped>
// 将css单位从rem转换为px
html {
  font-size: 7.5px;
}
.login {
  width: 100%;
  height: 100%;
  background: radial-gradient(
    100% 100% at 100% 100%,
    #88beff 10%,
    #61abff 49%,
    #5cc7ff 100%
  );
  .login-content {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 0 32rpx;
    box-sizing: border-box;
    .login-title {
      font-size: 48rpx;
      font-weight: bold;
      color: #333;
      margin-bottom: 64rpx;
    }
    .login-form {
      width: 100%;
      .form-item {
        width: 100%;
        height: 96rpx;
        background: #fff;
        border-radius: 8rpx;
        margin-bottom: 24rpx;
        display: flex;
        align-items: center;
        padding: 0 32rpx;
        box-sizing: border-box;
        .form-item-icon {
          width: 32rpx;
          height: 32rpx;
          margin-right: 16rpx;
        }
        .form-item-input {
          flex: 1;
          height: 100%;
          font-size: 28rpx;
          color: #333;
        }
      }
      .form-item-code {
        width: 100%;
        height: 96rpx;
        background: #fff;
        border-radius: 8rpx;
        margin-bottom: 32rpx;
        display: flex;
        align-items: center;
        padding: 0 32rpx;
        box-sizing: border-box;
        .form-item-icon {
          width: 32rpx;
          height: 32rpx;
          margin-right: 16rpx;
        }
        .form-item-input {
          flex: 1;
          height: 100%;
          font-size: 28rpx;
          color: #333;
        }
        .form-item-code-btn {
          width: 160rpx;
          height: 64rpx;
          background: #007aff;
          border-radius: 8rpx;
          font-size: 24rpx;
          color: #fff;
          display: flex;
          align-items: center;
          justify-content: center;
        }
      }
      .form-item-btn {
        width: 100%;
        height: 96rpx;
        background: #007aff;
        border-radius: 8rpx;
        font-size: 32rpx;
        color: #fff;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 32rpx;
      }
      .form-item-link {
        width: 100%;
        height: 96rpx;
        display: flex;
        align-items: center;
        justify-content: space-between;
        .form-item-link-text {
          font-size: 28rpx;
          color: #007aff;
        }
      }
    }
    .app-logo {
      display: flex;
      justify-content: center;
      align-items: center;
      flex-direction: column;
      margin-bottom: 40rpx;
      padding-top: 88rpx;
      .logo-icon {
        width: 35rpx;
        height: 35rpx;
        border-radius: 50%;
        background: #ee840e;
        margin-bottom: -10rpx;
        z-index: 2;
      }
      image {
        width: 187rpx;
        height: 144rpx;
        z-index: 1;
      }
    }
    .app-desc {
      font-size: 28rpx;
      color: #fff;
      margin-bottom: 44rpx;
      text-align: center;
    }
    .app-name {
      font-size: 40rpx;
      color: #fff;
      margin-bottom: 36rpx;
      text-align: center;
      font-weight: bold;
    }
    .link-view {
      width: 100%;
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-top: 46rpx;
      .link-item {
        font-size: 28rpx;
        color: #fff;
      }
    }
    .other-login {
      width: 100%;
      margin-top: 36rpx;
      .other-login-title {
        font-size: 28rpx;
        color: #fff;
        text-align: center;
      }
      .other-login-view {
        margin-top: 36rpx;
        display: flex;
        justify-content: space-around;
        align-items: center;
        padding: 0 50rpx;
        .other-login-item {
          width: 59rpx;
          height: 59rpx;
          image {
            width: 100%;
            height: 100%;
          }
        }
      }
    }
    .footer-icon {
      width: 100%;
      margin-top: 27rpx;
      image {
        width: 100%;
        height: 132rpx;
      }
    }
  }
}
</style>
