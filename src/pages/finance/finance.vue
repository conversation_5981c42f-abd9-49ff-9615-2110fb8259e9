<template>
  <view class="finance-page">
    <!-- 顶部导航栏 -->
    <uni-nav-bar
      :border="false"
      title="财务管理"
      background-color="var(--primary-color)"
      color="#fff"
      status-bar
      fixed
    />
    
    <!-- 数据概览卡片 -->
    <view class="data-overview">
      <view class="overview-item">
        <text class="amount">605321</text>
        <text class="label">本月收入</text>
      </view>
      <view class="overview-item">
        <text class="amount">57324</text>
        <text class="label">本月支出</text>
      </view>
      <view class="overview-item">
        <text class="amount">553240</text>
        <text class="label">本月毛利</text>
      </view>
    </view>

    <!-- 功能菜单 -->
    <view class="menu-grid">
      <view class="menu-item" @tap="onNavigateTo('/pages/finance/record')">
        <view class="icon-wrapper blue">
          <uni-icons type="list" size="24" color="#fff" />
        </view>
        <text class="menu-label">财务记录</text>
      </view>
      
      <view class="menu-item" @tap="onNavigateTo('/pages/finance/account')">
        <view class="icon-wrapper green">
          <uni-icons type="wallet" size="24" color="#fff" />
        </view>
        <text class="menu-label">挂账收款</text>
      </view>
      
      <view class="menu-item" @tap="onNavigateTo('/pages/finance/account')">
        <view class="icon-wrapper light-green">
          <uni-icons type="compose" size="24" color="#fff" />
        </view>
        <text class="menu-label">财务记账</text>
      </view>
      
      <view class="menu-item" @tap="onNavigateTo('/pages/finance/hours')">
        <view class="icon-wrapper gray">
          <uni-icons type="staff" size="24" color="#fff" />
        </view>
        <text class="menu-label">工时统计</text>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
const onNavigateTo = (url: string) => {
  uni.navigateTo({ url });
};
</script>

<style lang="scss" scoped>
.finance-page {
  min-height: 100vh;
  background-color: #f5f5f5;
}

// 数据概览样式
.data-overview {
  margin: 20rpx;
  padding: 30rpx 20rpx;
  background-color: #fff;
  border-radius: 12rpx;
  display: flex;
  justify-content: space-between;
  
  .overview-item {
    flex: 1;
    text-align: center;
    
    .amount {
      display: block;
      font-size: 32rpx;
      color: #ff6b00;
      font-weight: bold;
      margin-bottom: 8rpx;
    }
    
    .label {
      font-size: 24rpx;
      color: #666;
    }
  }
}

// 功能菜单样式
.menu-grid {
  margin: 20rpx;
  display: flex;
  flex-wrap: wrap;
  background-color: #fff;
  border-radius: 12rpx;
  padding: 20rpx;
  
  .menu-item {
    width: 25%;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 20rpx 0;
    
    .icon-wrapper {
      width: 80rpx;
      height: 80rpx;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-bottom: 16rpx;
      
      &.blue {
        background-color: #1989fa;
      }
      
      &.green {
        background-color: #07c160;
      }
      
      &.light-green {
        background-color: #4cd964;
      }
      
      &.gray {
        background-color: #909399;
      }
    }
    
    .menu-label {
      font-size: 24rpx;
      color: #333;
    }
  }
}

// 导航栏样式
:deep(.uni-nav-bar__content) {
  background-color: #1989fa !important;
}

:deep(.uni-nav-bar__header) {
  background-color: #1989fa !important;
}

:deep(.uni-nav-bar-text) {
  color: #fff !important;
}

:deep(.uni-icons) {
  color: #fff !important;
}
</style>
