<template>
  <view class="checkin-page">
    <!-- 顶部导航栏 -->
    <view class="nav-bar">
      <view class="nav-content">
        <view class="nav-left" @click="handleBack">
          <uni-icons type="left" size="18" color="#fff" />
        </view>
        <view class="nav-title">工分任务</view>
        <view class="nav-right"></view>
      </view>
    </view>

    <!-- 顶部任务卡片 -->
    <view class="task-header-card">
      <view class="task-days">
        <text class="days-number">{{ consecutiveDays }}</text>
        <text class="days-text">天</text>
      </view>
      <view class="task-info">
        <view class="task-title">每日签到获奖励</view>
        <view class="task-desc"
          >连续签到{{ totalDays }}天可获得{{ totalReward }}个工分</view
        >
      </view>
      <view class="task-action">
        <button
          class="checkin-btn"
          :class="{ disabled: hasCheckedIn }"
          @click="handleCheckin"
          :disabled="hasCheckedIn"
        >
          {{ hasCheckedIn ? "已签到" : "签到" }}
        </button>
      </view>
    </view>

    <!-- 月份切换 -->
    <view class="month-section">
      <view
        class="month-item"
        :class="{ active: currentMonthIndex === 0 }"
        @click="setMonth(0)"
      >
        <text class="month-text">2024/07</text>
      </view>
      <view
        class="month-item"
        :class="{ active: currentMonthIndex === 1 }"
        @click="setMonth(1)"
      >
        <text class="month-text">2024/08</text>
      </view>
      <view
        class="month-item"
        :class="{ active: currentMonthIndex === 2 }"
        @click="setMonth(2)"
      >
        <text class="month-text">2024/09</text>
      </view>
    </view>

    <!-- 日历组件 -->
    <view class="calendar-section">
      <view class="calendar-grid">
        <!-- 星期标题 -->
        <view class="week-header">
          <text v-for="week in weekDays" :key="week" class="week-day">{{
            week
          }}</text>
        </view>

        <!-- 日期网格 -->
        <view class="date-grid">
          <view
            v-for="(date, index) in calendarDates"
            :key="index"
            class="date-item"
            :class="{
              'other-month': date.isOtherMonth,
              today: date.isToday,
              checked: date.isChecked,
              'current-month': !date.isOtherMonth,
            }"
          >
            <text class="date-number">{{ date.day }}</text>
            <view
              v-if="date.isChecked && !date.isOtherMonth"
              class="check-mark"
            >
              ✓
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 签到规则说明 -->
    <view class="rules-section">
      <view class="rules-title">签到规则</view>
      <view class="rules-list">
        <text class="rule-item"
          >1.
          每日普通签到可获得1个工分，连续签到（两周）达到14天可获得10个工分。</text
        >
        <text class="rule-item">2. 签到任务每天可完成1次，从0点开始计算。</text>
        <text class="rule-item"
          >3. 连续签到中断后，需要重新开始计算连续天数。</text
        >
        <text class="rule-item"
          >4.
          在有网络（月网网络、月网网络）计算过程中，出现一次未签到，将会从额外工分奖励，一个月共计算次数外奖励过1分钟会。</text
        >
        <text class="rule-item"
          >5.
          工分币可以用作商品兑换，兑换规则详情工分商城产品兑换说明，本规则最终解释权归生活助手所有。</text
        >
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from "vue"

// 响应式数据
const consecutiveDays = ref(3) // 连续签到天数
const totalDays = ref(14) // 总目标天数
const totalReward = ref(10) // 总奖励工分
const hasCheckedIn = ref(false) // 今日是否已签到
const currentDate = ref(new Date())
const currentMonthIndex = ref(1) // 当前选中的月份索引，默认选中2024/08
const checkedDates = ref<string[]>([
  "2024-08-06",
  "2024-08-07",
  "2024-08-08",
  "2024-08-09",
  "2024-08-10",
  "2024-08-12",
  "2024-08-13",
  "2024-08-14",
  "2024-08-15",
  "2024-08-16",
]) // 已签到日期

// 星期标题
const weekDays = ["日", "一", "二", "三", "四", "五", "六"]

// 生成日历数据
const calendarDates = computed(() => {
  const year = currentDate.value.getFullYear()
  const month = currentDate.value.getMonth()
  const today = new Date()

  // 获取当月第一天和最后一天
  const firstDay = new Date(year, month, 1)
  const lastDay = new Date(year, month + 1, 0)

  // 获取第一天是星期几
  const firstDayWeek = firstDay.getDay()

  const dates = []

  // 添加上个月的日期（填充）
  const prevMonth = new Date(year, month - 1, 0)
  for (let i = firstDayWeek - 1; i >= 0; i--) {
    const day = prevMonth.getDate() - i
    dates.push({
      day,
      isOtherMonth: true,
      isToday: false,
      isChecked: false,
    })
  }

  // 添加当月日期
  for (let day = 1; day <= lastDay.getDate(); day++) {
    const dateStr = `${year}-${String(month + 1).padStart(2, "0")}-${String(
      day
    ).padStart(2, "0")}`
    const isToday =
      year === today.getFullYear() &&
      month === today.getMonth() &&
      day === today.getDate()

    dates.push({
      day,
      isOtherMonth: false,
      isToday,
      isChecked: checkedDates.value.includes(dateStr),
    })
  }

  // 添加下个月的日期（填充到42个格子）
  const remainingDays = 42 - dates.length
  for (let day = 1; day <= remainingDays; day++) {
    dates.push({
      day,
      isOtherMonth: true,
      isToday: false,
      isChecked: false,
    })
  }

  return dates
})

// 方法
const handleBack = () => {
  uni.navigateBack()
}

const handleCheckin = async () => {
  if (hasCheckedIn.value) return

  try {
    // 这里调用签到API
    // const res = await checkinAPI();

    // 模拟签到成功
    hasCheckedIn.value = true
    consecutiveDays.value += 1

    // 添加今日签到记录
    const today = new Date()
    const todayStr = `${today.getFullYear()}-${String(
      today.getMonth() + 1
    ).padStart(2, "0")}-${String(today.getDate()).padStart(2, "0")}`
    checkedDates.value.push(todayStr)

    uni.showToast({
      title: "签到成功！获得1工分",
      icon: "success",
      duration: 2000,
    })
  } catch (error) {
    uni.showToast({
      title: "签到失败，请重试",
      icon: "none",
    })
  }
}

const setMonth = (index: number) => {
  currentMonthIndex.value = index
  const months = [
    { year: 2024, month: 6 }, // 2024/07 (月份从0开始)
    { year: 2024, month: 7 }, // 2024/08
    { year: 2024, month: 8 }, // 2024/09
  ]
  const selectedMonth = months[index]
  currentDate.value = new Date(selectedMonth.year, selectedMonth.month, 1)
}

// 页面初始化
onMounted(() => {
  // 设置默认显示8月份
  setMonth(1)

  // 检查今日是否已签到
  const today = new Date()
  const todayStr = `${today.getFullYear()}-${String(
    today.getMonth() + 1
  ).padStart(2, "0")}-${String(today.getDate()).padStart(2, "0")}`
  hasCheckedIn.value = checkedDates.value.includes(todayStr)
})
</script>

<style lang="scss" scoped>
.checkin-page {
  min-height: 100vh;
  background: #f5f5f5;
}

.nav-bar {
  background: linear-gradient(135deg, #086bdb 0%, #4a9eff 100%);
  padding-top: var(--status-bar-height);

  .nav-content {
    height: 88rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 24rpx;

    .nav-left {
      width: 60rpx;
      height: 60rpx;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .nav-title {
      font-size: 36rpx;
      font-weight: bold;
      color: #fff;
    }

    .nav-right {
      width: 60rpx;
    }
  }
}

.month-section {
  display: flex;
  background: #fff;
  margin: 24rpx;
  border-radius: 16rpx;
  overflow: hidden;

  .month-item {
    flex: 1;
    text-align: center;
    padding: 24rpx 0;
    background: #f8f9fa;
    position: relative;

    .month-text {
      font-size: 28rpx;
      color: #666;
    }

    &.active {
      background: #086bdb;

      .month-text {
        color: #fff;
        font-weight: bold;
      }
    }

    &:not(:last-child)::after {
      content: "";
      position: absolute;
      right: 0;
      top: 50%;
      transform: translateY(-50%);
      width: 1px;
      height: 40rpx;
      background: #e0e0e0;
    }
  }
}

.task-header-card {
  background: linear-gradient(135deg, #086bdb 0%, #4a9eff 100%);
  margin: 24rpx;
  border-radius: 16rpx;
  padding: 32rpx;
  display: flex;
  align-items: center;
  box-shadow: 0 8rpx 24rpx rgba(8, 107, 219, 0.3);

  .task-days {
    display: flex;
    align-items: baseline;
    margin-right: 24rpx;

    .days-number {
      font-size: 72rpx;
      font-weight: bold;
      color: #fff;
      line-height: 1;
    }

    .days-text {
      font-size: 32rpx;
      color: #fff;
      margin-left: 8rpx;
    }
  }

  .task-info {
    flex: 1;

    .task-title {
      font-size: 32rpx;
      color: #fff;
      font-weight: bold;
      margin-bottom: 8rpx;
    }

    .task-desc {
      font-size: 26rpx;
      color: rgba(255, 255, 255, 0.8);
    }
  }

  .task-action {
    .checkin-btn {
      background: #fff;
      color: #086bdb;
      font-size: 28rpx;
      font-weight: bold;
      border-radius: 32rpx;
      padding: 0 32rpx;
      height: 64rpx;
      line-height: 64rpx;
      border: none;

      &.disabled {
        background: rgba(255, 255, 255, 0.3);
        color: rgba(255, 255, 255, 0.8);
      }
    }
  }
}

.calendar-section {
  background: #fff;
  margin: 0 24rpx 24rpx 24rpx;
  border-radius: 16rpx;
  overflow: hidden;

  .calendar-grid {
    padding: 24rpx;

    .week-header {
      display: flex;
      margin-bottom: 16rpx;

      .week-day {
        flex: 1;
        text-align: center;
        font-size: 26rpx;
        color: #666;
        font-weight: 500;
      }
    }

    .date-grid {
      display: flex;
      flex-wrap: wrap;
      gap: 8rpx 0;

      .date-item {
        width: calc(100% / 7);
        height: 80rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        position: relative;
        border-radius: 8rpx;

        .date-number {
          font-size: 28rpx;
          color: #333;
        }

        &.other-month .date-number {
          color: #ccc;
        }

        &.today {
          background: #e3f2fd;

          .date-number {
            color: #086bdb;
            font-weight: bold;
          }
        }

        &.checked {
          background: #086bdb;
          border-radius: 50%;

          .date-number {
            color: #fff;
            font-weight: bold;
          }

          .check-mark {
            position: absolute;
            bottom: 8rpx;
            right: 8rpx;
            font-size: 20rpx;
            color: #fff;
            font-weight: bold;
          }
        }
      }
    }
  }
}

.rules-section {
  background: #fff;
  margin: 0 24rpx;
  border-radius: 16rpx;
  padding: 24rpx;

  .rules-title {
    font-size: 30rpx;
    font-weight: bold;
    color: #333;
    margin-bottom: 16rpx;
  }

  .rules-list {
    .rule-item {
      display: block;
      font-size: 26rpx;
      color: #666;
      line-height: 1.6;
      margin-bottom: 12rpx;
      text-indent: 0;

      &:last-child {
        margin-bottom: 0;
      }
    }
  }
}
</style>
